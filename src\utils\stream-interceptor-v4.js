import { info, error } from './logger.js'
import config from '../config.js'
import { modifyBodyForModel } from './body-modifier.js'
import { v4 as uuidv4 } from 'uuid'
import { compatEvaluate } from './browser.js'
/**
 * StreamInterceptorV4 - 直接构建并执行 API 请求，而不是拦截现有请求
 * 这个版本会在浏览器中直接构建 fetch 请求并执行
 */
export class StreamInterceptorV4 {

  /**
   * @param {import('playwright').Page} page Playwright 页面对象
   * @param {function(string): void} onStreamChunk 流数据块回调
   * @param {function(): void} onStreamEnd 流结束回调
   * @param {Object} opts 选项参数
   * @param {string} [opts.model] 模型 ID。
   * @param {number} [opts.temperature] 温度值。
   * @param {string} [opts.systemPrompt] 系统指令。
   * @param {string} [opts.historyPrompt] 整合的指令。
   * @param {Array} [opts.messages] 整合的指令。
   */
  constructor(page, onStreamChunk, onStreamEnd, opts = {}) {
    this.page = page;
    this.onStreamChunk = onStreamChunk;
    this.onStreamEnd = onStreamEnd;
    this.opts = opts;
    this.uniqueId = `interceptor_v4_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    this.chunkCallbackName = `__onStreamChunk_${this.uniqueId}`;
    this.endCallbackName = `__onStreamEnd_${this.uniqueId}`;
    this.handlerKey = `__streamHandler_${this.uniqueId}`;
    this.isInjected = false;
    this.isActive = false;
    this.consoleListener = null; // Firefox 兼容性：console 消息监听器
    this.isFirefox = this._isFirefoxBrowser(); // 检测是否为 Firefox
  }

  /**
   * 检测是否为 Firefox 浏览器
   * @returns {boolean}
   * @private
   */
  _isFirefoxBrowser() {
    return config.browser.type === 'firefox' || config.browser.type === 'camoufox';
  }

  /**
   * 设置 Firefox 兼容性的 console 消息监听器
   * @private
   */
  _setupFirefoxConsoleListener() {
    if (!this.isFirefox || this.consoleListener) return;

    this.consoleListener = (msg) => {
      if (msg.type() === 'log') {
        const text = msg.text();
        if (text.startsWith('[STREAM_CHUNK]')) {
          const data = text.replace('[STREAM_CHUNK]', '');
          this.onStreamChunk(data);
        } else if (text.startsWith('[STREAM_END]')) {
          this.onStreamEnd();
        }
      }
    };

    this.page.on('console', this.consoleListener);
    info(`[InterceptorV4] Firefox 兼容性：已设置 console 消息监听器 (ID: ${this.uniqueId})`);
  }

  /**
   * 移除 Firefox 兼容性的 console 消息监听器
   * @private
   */
  _removeFirefoxConsoleListener() {
    if (!this.isFirefox || !this.consoleListener) return;

    this.page.off('console', this.consoleListener);
    this.consoleListener = null;
    info(`[InterceptorV4] Firefox 兼容性：已移除 console 消息监听器 (ID: ${this.uniqueId})`);
  }

  /**
   * 将脚本注入到页面中
   * @private
   */
  async _inject() {
    if (this.isInjected) return;

    info(`[InterceptorV4] 注入直接请求脚本 (ID: ${this.uniqueId})...`);

    if (this.isFirefox) {
      // Firefox 兼容性：使用 console 消息作为回调机制
      info(`[InterceptorV4] Firefox 兼容性：使用 console 消息回调 (ID: ${this.uniqueId})`);
      this._setupFirefoxConsoleListener();
    } else {
      // 非 Firefox：使用标准的 exposeFunction
      await this.page.exposeFunction(this.chunkCallbackName, this.onStreamChunk);
      await this.page.exposeFunction(this.endCallbackName, this.onStreamEnd);
    }

    const injectionPayload = {
      chunkCallbackName: this.chunkCallbackName,
      endCallbackName: this.endCallbackName,
      handlerKey: this.handlerKey,
      isFirefox: this.isFirefox,
    };

    const browserScript = this._getBrowserScript();
    await compatEvaluate(this.page, browserScript, injectionPayload, false);

    this.isInjected = true;
    info(`[InterceptorV4] 脚本注入完成 (ID: ${this.uniqueId})`);
  }

  /**
   * 激活拦截器
   */
  async activate() {
    if (this.isActive) return;
    await this._inject();

    info(`[InterceptorV4] 激活直接请求处理器 (ID: ${this.uniqueId})...`);
    this.isActive = true;
    info(`[InterceptorV4] 直接请求处理器已激活 (ID: ${this.uniqueId})`);
  }

  /**
   * 停用拦截器
   */
  async deactivate() {
    if (!this.isActive) return;

    info(`[InterceptorV4] 停用直接请求处理器 (ID: ${this.uniqueId})...`);
    try {
      // 移除 Firefox 兼容性的 console 监听器
      this._removeFirefoxConsoleListener();

      await compatEvaluate(this.page, (handlerKey) => {
        if (window[handlerKey]) {
          delete window[handlerKey];
        }
      }, this.handlerKey, false);
      this.isActive = false;
      info(`[InterceptorV4] 直接请求处理器已停用 (ID: ${this.uniqueId})`);
    } catch (err) {
      error(`[InterceptorV4] 停用时发生错误 (可能页面已关闭): ${err.message}`);
    }
  }

  /**
   * 执行直接请求
   * @param {Array} messages 原始消息数组
   */
  async executeDirectRequest() {
    if (!this.isActive) {
      throw new Error('InterceptorV4 未激活');
    }

    info(`[InterceptorV4] 开始执行直接请求...`);

    // 构建请求数据
    const requestData = this._buildRequestData();

    // 将请求数据转换为 JSON 字符串
    const requestDataStr = JSON.stringify(requestData);

    // 在浏览器中执行请求
    await compatEvaluate(this.page, async (payload) => {
      const { handlerKey, requestDataStr } = payload;

      if (!window[handlerKey]) {
        console.error('[InterceptorV4] 处理器未找到');
        return;
      }

      await window[handlerKey].executeRequest(requestDataStr);
    }, {
      handlerKey: this.handlerKey,
      requestDataStr: requestDataStr
    }, false);
  }

  /**
   * 构建请求数据
   * @param {Array} messages 原始消息数组
   * @returns {Object} 请求数据对象
   * @private
   */
  _buildRequestData() {
    const sessionId = uuidv4();
    const messages = this.opts.messages || [];

    // 获取默认模型ID，如果没有指定模型则使用默认模型
    const modelName = this.opts.model || config.app.defaultModel;
    const modelId = config.modelsAIds[modelName] || config.modelsAIds[config.app.defaultModel];

    // 转换消息格式并建立父子关系
    const convertedMessages = [];
    let previousMessageId = null;
    let lastUserMessageId = null;
    let lastAssistantMessageId = null;

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      const messageId = uuidv4();

      const convertedMessage = {
        id: messageId,
        role: message.role === "system" ? "user" : message.role,
        content: message.content,
        experimental_attachments: [],
        parentMessageIds: previousMessageId ? [previousMessageId] : [],
        participantPosition: "a",
        modelId: message.role === "assistant" ? modelId : null,
        evaluationSessionId: sessionId,
        status: message.role === "assistant" ? "success" : "pending",
        failureReason: null
      };

      convertedMessages.push(convertedMessage);
      previousMessageId = messageId;

      // 记录最后的用户和助手消息ID
      if (message.role === "user") {
        lastUserMessageId = messageId;
      } else if (message.role === "assistant") {
        lastAssistantMessageId = messageId;
      }
    }

    // 添加新的助手消息（用于接收响应）
    const newAssistantMessageId = uuidv4();
    convertedMessages.push({
      id: newAssistantMessageId,
      role: "assistant",
      content: "",
      experimental_attachments: [],
      parentMessageIds: previousMessageId ? [previousMessageId] : [],
      participantPosition: "a",
      modelId: modelId,
      evaluationSessionId: sessionId,
      status: "pending",
      failureReason: null
    });

    return {
      id: sessionId,
      mode: "direct",
      modelAId: modelId,
      userMessageId: lastUserMessageId || uuidv4(),
      modelAMessageId: newAssistantMessageId,
      messages: convertedMessages,
      modality: "chat"
    };
  }

  /**
   * 返回要注入到浏览器页面的脚本函数
   * @returns {function}
   * @private
   */
  _getBrowserScript() {
    return (payload) => {
      const { chunkCallbackName, endCallbackName, handlerKey, isFirefox } = payload;

      // 清理之前的处理器
      if (window[handlerKey]) {
        delete window[handlerKey];
      }

      // 根据浏览器类型设置回调函数
      let onChunkCallback, onEndCallback;

      if (isFirefox) {
        // Firefox 兼容性：使用 console 消息
        onChunkCallback = (data) => {
          console.log(`[STREAM_CHUNK]${data}`);
        };
        onEndCallback = () => {
          console.log('[STREAM_END]');
        };
        console.log(`[InterceptorV4] Firefox 兼容性：使用 console 消息回调 (${handlerKey})`);
      } else {
        // 标准模式：使用 exposeFunction
        onChunkCallback = window[chunkCallbackName];
        onEndCallback = window[endCallbackName];
        console.log(`[InterceptorV4] 设置直接请求处理器: ${handlerKey},${onEndCallback},${onChunkCallback}`);
      }

      window[handlerKey] = {
        executeRequest: async (requestDataStr) => {
          try {
            console.log('🚀 [InterceptorV4] 开始执行直接 API 请求...');

            // 构建请求头
            const headers = {
              "accept": "*/*",
              "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
              "cache-control": "no-cache",
              "content-type": "text/plain;charset=UTF-8",
              "pragma": "no-cache",
              "priority": "u=1, i",
              "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
              "sec-ch-ua-arch": '"x86"',
              "sec-ch-ua-bitness": '"64"',
              "sec-ch-ua-full-version": '"138.0.3351.95"',
              "sec-ch-ua-full-version-list": '"Not)A;Brand";v="8.0.0.0", "Chromium";v="138.0.7204.158", "Microsoft Edge";v="138.0.3351.95"',
              "sec-ch-ua-mobile": "?0",
              "sec-ch-ua-model": '""',
              "sec-ch-ua-platform": '"Windows"',
              "sec-ch-ua-platform-version": '"19.0.0"',
              "sec-fetch-dest": "empty",
              "sec-fetch-mode": "cors",
              "sec-fetch-site": "same-origin",
              //"cookie": cookies,
              "Referer": window.location.href
            };

            // 执行 fetch 请求
            const response = await fetch("https://lmarena.ai/api/stream/create-evaluation", {
              method: "POST",
              headers: headers,
              body: requestDataStr
            });

            if (!response.body) {
              console.error('[InterceptorV4] 响应没有 body');
              if (onEndCallback) onEndCallback();
              return;
            }

            console.log('📡 [InterceptorV4] 开始读取流式响应...');

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let streamEnded = false;

            const finalizeStream = () => {
              if (streamEnded) return;
              streamEnded = true;
              console.log('🏁 [InterceptorV4] 流已结束');
              if (onEndCallback) {
                onEndCallback();
              }
            };

            // 读取流数据
            while (true) {
              const { done, value } = await reader.read();

              if (done) {
                console.log('🏁 [InterceptorV4] 流读取完成');
                finalizeStream();
                break;
              }

              const chunk = decoder.decode(value, { stream: true });

              // 发送数据块回调
              if (onChunkCallback && chunk) {
                onChunkCallback(chunk);
              }
            }

          } catch (error) {
            console.error('[InterceptorV4] 直接请求出错:', error);
            if (onEndCallback) {
              onEndCallback();
            }
          }
        }
      };

      console.log(`[InterceptorV4] 直接请求处理器已设置完成: ${handlerKey}`);
    };
  }
}