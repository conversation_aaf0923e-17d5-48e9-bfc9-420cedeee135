name: Build and Deploy to HF Spaces
on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Deploy to Hugging Face Spaces
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        run: |
          # 克隆目标 HF Space 仓库到一个临时目录
          git clone https://zhangxiaoduan:$<EMAIL>/spaces/zhangxiaoduan/lmarena hf-repo
          
          cd hf-repo
          
          # 清理旧文件，除了 .git 目录
          find . -not -path "./.git/*" -not -name ".git" -not -name "README.md" -delete

          # 从原始工作区复制构建产物和必要文件到 HF 仓库
          echo "Copying necessary files to HF repo..."
          cp ../package.json .
          cp ../package-lock.json .
          cp ../Dockerfile .
          cp ../*.sh .
          cp -r ../public .
          cp -r ../dist .
          
          # 如果 cookies.json 存在，也复制它
          if [ -f ../cookies.json ]; then
            cp ../cookies.json .
          fi
          
          # 配置 Git
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"

          # 添加、提交并推送文件到 Hugging Face
          git add .
          
          # 如果没有变化，则退出，避免空提交
          if git diff --staged --quiet; then
            echo "No changes to commit. Exiting."
            exit 0
          fi
          
          git commit -m "Deploy: Update from GitHub Actions build"
          git push