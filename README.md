# LMArena 2 API

一个基于 H3 v2beta 和 Playwright 的代理服务器，将  LMArena 转换为 OpenAI 兼容的 API。

## 功能特性

- 🚀 基于 H3 v2beta 构建的高性能服务器

- 🎭 使用 Playwright 自动化浏览器操作

- 📡 支持流式和非流式响应

- 🔄 OpenAI API 兼容格式

- 🛡️ 错误处理和资源清理

- 🔧 模块化架构设计

- 🌍 环境变量配置支持

## 安装

```bash
# 克隆项目
git clone <repository-url>
cd lmarena2api

# 安装依赖
npm install

# 安装 Playwright 浏览器
npx playwright install chromium

# 复制环境变量配置文件
cp .env.example .env
```

## 使用方法

### 启动服务器

```bash
# 开发模式（带热重载）
npm run dev

# 生产模式
npm start
```

服务器将在 `http://localhost:3096` 启动。

### API 端点

#### 健康检查

```
GET /health
```

#### 模型列表

```
GET /v1/models
```

获取所有可用模型的列表，遵循 OpenAI API 规范。

**响应示例**：

```json
{
  "object": "list",
  "data": [
    {
      "id": "gemini-2.5-pro",
      "object": "model",
      "created": **********,
      "owned_by": "google",
      "permission": [],
      "root": "gemini-2.5-pro",
      "parent": null
    }
  ]
}
```

#### 单个模型信息

```
GET /v1/models/{model}
```

获取指定模型的详细信息。

**参数**：

- `model` (路径参数): 模型ID，如 `gemini-2.5-pro`

**响应示例**：

```json
{
  "id": "gemini-2.5-pro",
  "object": "model",
  "created": **********,
  "owned_by": "google",
  "permission": [],
  "root": "gemini-2.5-pro",
  "parent": null
}
```

#### 聊天完成

```
POST /v1/chat/completions
```

**认证要求**: 需要在请求头中包含有效的 API Token

请求头：

```
Authorization: Bearer your_secret_api_token_here
Content-Type: application/json
```

请求格式（OpenAI 兼容）：

```json
{
  "model": "gemini-2.5-pro",
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下你自己"
    }
  ],
  "stream": true
}
```

**cURL 示例**：

```bash
curl -X POST http://localhost:3095/v1/chat/completions \
  -H "Authorization: Bearer your_secret_api_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "你好"
      }
    ],
    "stream": false
  }'
```

## 配置

### 环境变量

复制 `.env.example` 到 `.env` 并根据需要修改配置：

```bash
# 服务器配置
PORT=3000
NODE_ENV=development

# 浏览器配置
HEADLESS=false
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# App 配置
WEB_URL=https://lmarena.ai/?mode=direct
PAGE_TIMEOUT=30000
DEFAULT_MODEL=gemini-2.5-pro

# CORS 配置
CORS_ORIGIN=*

# API 认证配置
API_TOKEN=your_secret_api_token_here

 
```

### API 认证配置

为了保护 API 端点，支持两种认证模式：

#### 模式一：API Token 认证（默认）

1. **设置强密码**: 在 `.env` 文件中设置一个强密码作为 API Token

2. **请求认证**: 所有对 `/v1/chat/completions` 的请求都需要在 `Authorization` 头中包含有效的 Bearer token

3. **安全建议**:

   - 使用至少 32 位的随机字符串作为 token

   - 定期更换 token

   - 不要在代码中硬编码 token

   - 不要将 `.env` 文件提交到版本控制系统

#### 模式二：APP_TOKEN 认证（多用户支持）

如果需要支持多用户，可以启用 APP_TOKEN 认证模式：

1. **启用 APP_TOKEN 模式**: 在 `.env` 文件中设置 `USE_APP_TOKEN=true`

2. **多用户支持**: 在此模式下，每个请求的 `Authorization` 头中的 Bearer token 将被视为用户的 APP_TOKEN token

3. **自动隔离**: 系统会为每个不同的 APP_TOKEN token 创建独立的浏览器上下文，实现用户间的完全隔离

4. **使用方式**:

   ```bash
   # 在 .env 文件中启用 APP_TOKEN 模式
   USE_APP_TOKEN=true
   
   # 客户端请求时，将用户的 APP_TOKEN token 作为 Bearer token
   curl -X POST http://localhost:3096/v1/chat/completions \
     -H "Authorization: Bearer user_app_token_here" \
     -H "Content-Type: application/json" \
     -d '{"messages": [{"role": "user", "content": "Hello"}]}'
   ```

**生成安全 Token 示例**：

```bash
# 使用 openssl 生成随机 token
openssl rand -hex 32

# 或使用 Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### 安全注意事项

- 🔒 **保护 Cookie 值**：这些 Cookie 包含您的  账户认证信息，请妥善保管

- 🚫 **不要分享**：不要将包含真实 Cookie 值的 `.env` 文件提交到版本控制系统

- ⏰ **定期更新**： 可能会定期更新这些 Cookie，如果遇到认证失败，请重新获取

- 🔄 **会话管理**：如果在其他地方登出  账户，这些 Cookie 可能会失效

## Docker 图形界面访问

本项目支持在 Docker 容器中使用 Xvfb 运行有头模式的浏览器，并提供 noVNC Web 界面进行远程访问。详细使用说明请参考 [图形界面访问指南](./XVFB_USAGE.md)。

### 快速开始

```bash
# 构建
docker build -t zhezzma/lmarena2api:latest .

# 基本有头模式
docker-compose up -d --build

# 带 noVNC Web 界面模式（推荐用于调试）
docker-compose -f docker-compose.yml -f docker-compose.novnc.yml up -d --build

# 开发模式 + noVNC Web 界面
docker-compose -f docker-compose.yml -f docker-compose.override.yml -f docker-compose.novnc-dev.yml up -d --build
```

### 主要特性

- 🖥️ **虚拟显示**：使用 Xvfb 在无图形界面的服务器上运行有头浏览器

- 🌐 **Web 界面**：通过 noVNC 在浏览器中查看和控制图形界面

- ⚙️ **灵活配置**：支持多种分辨率和显示配置

### 访问方式

|模式|访问地址|说明|
|-|-|-|
|**应用服务**|http://localhost:7860|主要 API 服务|
|**noVNC Web**|http://localhost:6080|通过浏览器查看图形界面|

详细使用说明请参考：

- [图形界面访问指南](./XVFB_USAGE.md)

- [容器内开发环境指南](./CONTAINER_DEVELOPMENT.md) - 适用于远程 VSCode、Codespaces 等

## 容器内开发

如果您在已有的 Docker 容器内开发（如远程 VSCode、GitHub Codespaces），可以直接在容器内安装和运行本项目：

### 快速开始

```bash
# 1. 克隆项目
git clone <repository-url>
cd lmarena2api

# 2. 运行一键安装脚本
./setup-container-dev.sh

# 3. 启动 noVNC 开发环境
./start-container-novnc.sh
```

### 访问方式

- **noVNC Web 界面**: http://localhost:6080 (需要端口转发)

- **应用服务**: http://localhost:7860 (需要端口转发)

### 端口转发

在 VSCode 中：

1. 按 `Ctrl+Shift+P` 打开命令面板

2. 输入 "Forward a Port"

3. 添加端口 `6080` 和 `7860`

详细说明请参考 [容器内开发环境指南](./CONTAINER_DEVELOPMENT.md)。

- 🔧 **开发友好**：开发模式自动启用有头模式和详细日志

## 许可证

MIT License